import { test, expect } from '@playwright/test';

test.describe('Vue App UI', () => {
  test('should display the main container', async ({ page }) => {
    await page.goto('/');
    await expect(page.locator('.container')).toBeVisible();
  });

  test('should add a new task', async ({ page }) => {
    await page.goto('/');
    const input = page.locator('input#task');
    const select = page.locator('select#select');
    const addButton = page.locator('button:has-text("Add")');
    const newTitle = `Test Task ${Date.now()}`;

    await input.fill(newTitle);
    await select.selectOption('medium');
    await addButton.click();
    // Wait for the new task to appear in the table
    const row = page.locator('tr', { has: page.locator(`td:has-text("${newTitle}")`) });
    await expect(row).toBeVisible();
    await expect(row.locator('td', { hasText: 'Medium' })).toBeVisible();
  });

  test('should show validation error for empty title', async ({ page }) => {
    await page.goto('/');
    const input = page.locator('input#task');
    const addButton = page.locator('button:has-text("Add")');
    await input.fill('');
    await addButton.click();
    await expect(page.locator('.invalid-feedback')).toHaveText('The title field is required');
  });

  test('should remove a task', async ({ page }) => {
    await page.goto('/');
    // Add a new task to ensure there is one to delete
    const input = page.locator('input#task');
    const select = page.locator('select#select');
    const addButton = page.locator('button:has-text("Add")');
    const newTitle = `Task To Delete ${Date.now()}`;
    await input.fill(newTitle);
    await select.selectOption('low');
    await addButton.click();
    // Wait for the new task to appear
    const row = page.locator('tr', { has: page.locator(`td:has-text("${newTitle}")`) });
    await expect(row).toBeVisible();
    // Click the remove button in the same row
    await row.locator('button:has-text("Remove")').click();
    // Wait for the row to disappear
    await expect(row).toBeHidden();
  });
});
